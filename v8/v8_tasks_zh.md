# 项目：V8 餐品计划生成实现

**描述：** 基于 `v8/meal-plan-generation-prd-unified.md` 的餐品计划生成的关键稳定性修复和性能优化。

**环境:**
*   **语言:** Swift
*   **Swift 版本:** 5.9
*   **平台:** iOS
*   **部署目标:** 17.0
*   **UI 框架:** SwiftUI
*   **并发模型:** Swift Concurrency (async/await, MainActor)
*   **构建系统:** XcodeGen (project.yml)

---

## 第一阶段：MVP - 关键稳定性与核心功能 (第 1-3 天)

### 任务 MVP-1: 起始日期选择与枚举
- **描述:** 添加 `userSelectedStartDate` 跟踪和起始日期选择器 UI。
- **优先级:** P0
- **天数:** 1
- **验收标准:**
    - 起始日期选择器范围限定在今天...今天+7天。
    - 遵循当日截止时间（截止时间后的餐品被跳过）。
    - 从选定的日期开始枚举。
- **待修改文件:**
    - `Features/RecipeGenerator/RecipeGeneratorView.swift`
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
    - `Services/RecipeRequestBuilder.swift`
- **PRD 参考:** FR-MVP-1, Section 5
- **状态:** 已完成

### 任务 MVP-2: 仅追加的合并策略
- **描述:** 为重叠的日期实现 `AppendAll` 合并策略。
- **优先级:** P0
- **天数:** 1
- **验收标准:**
    - 重叠的日期保留现有项目。
    - 新项目与现有项目并排出现。
    - 在 MVP 阶段不发生替换。
- **待修改文件:**
    - `Services/PlanStore.swift`
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- **PRD 参考:** FR-MVP-2, Section 5
- **状态:** 已完成

### 任务 MVP-3: 为餐品计划禁用预取
- **描述:** 添加 `prefetchDetails` 参数并在结构化路径中禁用它。
- **优先级:** P0
- **天数:** 1
- **验收标准:**
    - 餐品计划路径执行零次详情预取调用。
    - 快速模式行为不变（仍然预取）。
    - 计划内容和槽位分配不变。
- **待修改文件:**
    - `Services/RecipeServiceAdapter.swift`
    - `Services/StructuredMealPlanGenerator.swift`
- **PRD 参考:** FR-MVP-7, Section 5
- **状态:** 已完成

### 任务 MVP-4: 每餐的错误隔离与资源上限
- **描述:** 添加健壮的错误处理并防止崩溃。
- **优先级:** P0
- **天数:** 2
- **验收标准:**
    - 复杂配置（3天；早餐2/午餐3/晚餐4）能完成且不崩溃。
    - 单餐失败不会导致整个运行过程崩溃。
    - 强制执行每餐最多12道菜的资源上限。
- **待修改文件:**
    - `Services/StructuredMealPlanGenerator.swift`
- **PRD 参考:** FR-MVP-5, Section 4A Issue 1, Section 9A code snippets
- **状态:** 已完成

### 任务 MVP-5: 生成超时保护
- **描述:** 添加带有边界的纳秒转换的超时辅助函数。
- **优先级:** P0
- **天数:** 2
- **验收标准:**
    - 生成在45秒内完成或优雅地超时。
    - 有界的纳秒转换（1毫秒到10分钟）。
    - 超时后抛出 `TimeoutError`。
- **待修改文件:**
    - `Utilities/AsyncTimeout.swift`
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- **PRD 参考:** FR-MVP-5, Section 9A timeout helper code
- **状态:** 已完成

### 任务 MVP-6: 配置变更检测
- **描述:** 实现生成指纹以防止导航到过时结果。
- **优先级:** P0
- **天数:** 2
- **验收标准:**
    - 配置变更会触发实际的生成过程。
    - 仅在新的生成成功后才进行导航。
    - 当配置更改时，不会跳转到之前的结果。
- **待修改文件:**
    - `Models/GenerationFingerprint.swift`
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- **PRD 参考:** FR-MVP-8, Section 4A Issue 2, Section 9A fingerprint code
- **状态:** 已完成

### 任务 MVP-7: 非阻塞式保存后摘要
- **描述:** 显示带有生成摘要的内联横幅。
- **优先级:** P0
- **天数:** 2
- **验收标准:**
    - 摘要按天×餐显示正确的数量。
    - “查看”按钮能导航到相关周。
    - 在3-5秒内自动消失。
- **待修改文件:**
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- **PRD 参考:** FR-MVP-3, Section 7
- **状态:** 已完成

### 任务 MVP-8: 修复“管理”按钮集成
- **描述:** 标准化日期索引以实现正确的槽位集合。
- **优先级:** P0
- **天数:** 2
- **验收标准:**
    - 生成后，“管理”功能立即显示生成的项目。
    - 删除/收藏/分享操作成功。
    - 日期键使用 `yyyy-MM-dd`, UTC, `en_US_POSIX` 格式。
- **待修改文件:**
    - `Views/SwipeableMealPlanCalendarView.swift`
- **PRD 参考:** FR-MVP-4, Section 4A Issue 3
- **状态:** 已完成

### 任务 MVP-9: Prompt 保真度 - 所有配置作为约束
- **描述:** 在 AI prompt 中包含所有用户配置。
- **优先级:** P0
- **天数:** 3
- **验收标准:**
    - Prompt 包含餐品类型指导。
    - 目标菜品数量反映在 prompt 中。
    - 当提供时，包含菜系和附加请求。
- **待修改文件:**
    - `Services/RecipeServiceAdapter.swift`
    - `Services/RecipeGenerationService.swift`
    - `Models/Recipe.swift`
- **PRD 参考:** FR-MVP-6
- **状态:** 已完成

### 任务 MVP-10: 单元测试 - 核心功能
- **描述:** 为 MVP 功能实现单元测试。
- **优先级:** P0
- **天数:** 3
- **验收标准:**
    - 指纹比较测试通过。
    - 预取标志测试验证了餐品计划与快速模式的不同行为。
    - 错误隔离测试防止了崩溃传播。
- **待修改文件:**
    - `Tests/MealPlanGenerationTests.swift`
- **PRD 参考:** Section 11A Unit Tests
- **状态:** 已完成

---

## 第二阶段：性能 - 性能与响应性优化 (第 4-6 天)

### 任务 PERF-1: 验证禁用预取的影响
- **描述:** 全面验证禁用预取功能正常工作。
- **优先级:** P1
- **天数:** 4
- **验收标准:**
    - 确认餐品计划路径执行0次预取调用。
    - 快速模式仍然预取详情。
    - 详情视图按需加载无误。
- **待修改文件:**
    - `Tests/RecipeServiceAdapterTests.swift`
- **PRD 参考:** FR-PERF Disable Detail Prefetch, Day 4 milestone
- **状态:** 已完成

### 任务 PERF-2: 并行化每餐生成
- **描述:** 使用 `TaskGroup` 并发运行不同餐品类型。
- **优先级:** P1
- **天数:** 5
- **验收标准:**
    - 总耗时约等于最长的单餐批处理时间，而不是总和。
    - 并发生成中无数据竞争。
- **待修改文件:**
    - `Services/StructuredMealPlanGenerator.swift`
- **PRD 参考:** FR-PERF Parallelize Per-Meal Generation
- **状态:** 已完成

### 任务 PERF-3: 主线程响应性
- **描述:** 在生成过程中移除主线程阻塞。
- **优先级:** P1
- **天数:** 5
- **验收标准:**
    - UI 在生成期间保持响应。
    - 取消操作在500毫秒内响应。
    - 主线程不因等待完整生成任务而阻塞。
- **待修改文件:**
    - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- **PRD 参考:** FR-PERF Keep MainActor Responsive

### 任务 PERF-4: PlanStore 后台处理
- **描述:** 将合并/编码操作移出主线程。
- **优先级:** P1
- **天数:** 6
- **验收标准:**
    - 保存过程中在主线程上没有可观察到的卡顿。
    - 合并和 JSON 编码在后台线程执行。
    - 最终写入在主线程上保持原子性。
- **待修改文件:**
    - `Services/PlanStore.swift`
- **PRD 参考:** FR-PERF Offload Heavy Work

### 任务 PERF-5: 集成测试 - 性能场景
- **描述:** 用真实场景验证性能改进。
- **优先级:** P1
- **天数:** 6
- **验收标准:**
    - 在午餐+晚餐场景中，AI 调用从≤10次降至4次。
    - 总耗时显著减少。
    - 验证取消响应时间<500毫秒。
- **待修改文件:**
    - `Tests/MealPlanPerformanceTests.swift`
- **PRD 参考:** Section 11A Integration Tests, Performance Scenario
